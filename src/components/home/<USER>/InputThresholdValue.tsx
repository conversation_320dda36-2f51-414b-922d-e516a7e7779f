"use client";
import React, { useEffect, useState } from "react";
import { getDownloadReport } from '@/api/home/<USER>/download/getDownloadReport';
import {
  MinusCircleIcon,
  PlusCircleIcon,
} from "@heroicons/react/20/solid";
import {
  SparklesIcon,
  CheckBadgeIcon,
} from "@heroicons/react/24/outline";

import Link from "next/link";
import { Input } from "@/components/ui/input";
import { useGenerateReport } from "@/hooks/home/<USER>/useGenerateReport";

interface InputThresholdValueProps {
  handleGenerateReport: () => void;
  setThresholdNumber: React.Dispatch<React.SetStateAction<number>>;
  thresholdNumber: number;
  fileNameCV?: string;
  isLoading: boolean;
  isReportGenerated: boolean;
  selectedRunId: number;
  fileNameZip?: string;
  projectId: number;
  useDefaultWeights?: any;
  rangeValuesrange1: any;
  rangeValuesrange2: any;
  info?: any;
  filecount?: any;
}

const InputThresholdValue: React.FC<InputThresholdValueProps> = ({
  setThresholdNumber,
  info,
  useDefaultWeights,
  rangeValuesrange1,
  rangeValuesrange2,
  thresholdNumber,
  fileNameCV,
  selectedRunId,
  fileNameZip,
  projectId,
  filecount
}) => {
  const [thresholdNumberDetails, setThresholdNumberDetails] = useState<number>(filecount || 1)
  const [isLoading, setIsLoading] = useState<boolean>(false);
  const [modalType, setModalType] = useState<string | null>(null);
  const [modalMessage, setModalMessage] = useState<string>("");
  const [isReportGenerated, setIsReportGenerated] = useState<boolean>(false);

  const generateReportData = useGenerateReport(
    selectedRunId,
    useDefaultWeights,
    rangeValuesrange1,
    rangeValuesrange2,
    thresholdNumberDetails,
    info
  );

  const handleSetThresholdNumberDetails = (numberValue: number) => {
    setThresholdNumberDetails(numberValue)
    setThresholdNumber(numberValue)
  }

  const handleGenerateReport = async () => {
    // Set loading to true when report generation starts
    setIsLoading(true);

    try {
      // Trigger the mutation to upload the data
      const response = await generateReportData.mutateAsync();

      if (response.msg) {
        setModalType("msg");  // Set the modal type to "msg"
        setModalMessage(response?.msg);  // Set the message from the response
        setIsReportGenerated(true);
      }

      if (response.err) {
        setModalType("err");  // Set the modal type to "err"
        setModalMessage(response?.err);
        setIsReportGenerated(false);
      }

      if (response.detail) {
        setModalType("err");  // Set the modal type to "err" for details
        setModalMessage(response?.detail);
        setIsReportGenerated(false);
      }

      // Check if the response has the 'success' property
      if (response && response.success) {
        const message = response.success;  // Access the success message
        // alert("Response Message: " + message);  // Show the success message in alert
      } else {
        setIsLoading(false);
        console.error("Unexpected response format", response);
        // alert("Unexpected response format.");
      }
    } catch (error) {
      console.error("Error generating report:", error);
      // alert("An error occurred while generating the report. Please try again.");
    } finally {
      // Stop loading regardless of success or failure
      setIsLoading(false);
    }
  };

  return (
    <div className="w-full">
      <div className="w-full max-w-4xl mx-auto">

        {!fileNameCV && (
          <div className="bg-white rounded-lg border border-gray-200 p-4 mb-4">
            <div className="flex items-center gap-3 mb-4">
              <h3 className="text-lg font-semibold text-gray-900">
                Generate Assessment Report
              </h3>
              <div className="px-2 py-1 bg-green-100 text-green-800 text-xs font-medium rounded">
                {filecount} CVs
              </div>
            </div>

            {/* Process Overview */}
            <div className="bg-blue-50 rounded-lg p-3 border border-blue-100 mb-4">
              <h4 className="text-sm font-semibold text-blue-900 mb-2">What happens next:</h4>
              <div className="grid grid-cols-1 md:grid-cols-3 gap-3 text-xs text-blue-800">
                <div className="flex items-center gap-2">
                  <div className="w-2 h-2 bg-blue-500 rounded-full"></div>
                  <span>AI analyzes all CVs against job requirements</span>
                </div>
                <div className="flex items-center gap-2">
                  <div className="w-2 h-2 bg-blue-500 rounded-full"></div>
                  <span>Candidates ranked by assessment criteria</span>
                </div>
                <div className="flex items-center gap-2">
                  <div className="w-2 h-2 bg-blue-500 rounded-full"></div>
                  <span>Detailed reports generated with insights</span>
                </div>
              </div>
            </div>

            <div className="grid grid-cols-1 lg:grid-cols-2 gap-4">
                <div className="space-y-3">
                  <div className="space-y-2">
                    <h4 className="text-sm font-semibold text-gray-900">Select Shortlist Size</h4>
                    <p className="text-xs text-gray-600">
                      Choose how many top-ranked candidates to include in your shortlist from {filecount} total CVs
                    </p>

                    {/* Shortlist guidance */}
                    <div className="bg-yellow-50 rounded p-2 border border-yellow-200">
                      <p className="text-xs text-yellow-800">
                        <strong>Tip:</strong> Industry standard is 3-5 candidates per position for optimal interview efficiency.
                      </p>
                    </div>

                    <div className="bg-gray-50 border border-gray-200 rounded-lg p-3">
                      <div className="flex items-center justify-center gap-3">
                        {/* Decrement Button */}
                        <button
                          onClick={() => handleSetThresholdNumberDetails(thresholdNumberDetails > 1 ? thresholdNumberDetails - 1 : 1)}
                          className="p-2 bg-blue-600 hover:bg-blue-700 text-white rounded-lg transition-all duration-200"
                        >
                          <MinusCircleIcon className="w-4 h-4" />
                        </button>

                        {/* Input Display */}
                        <div className="flex flex-col items-center">
                          <Input
                            type="number"
                            min="1"
                            max={filecount}
                            value={thresholdNumberDetails}
                            onChange={(e) => handleSetThresholdNumberDetails(Number(e.target.value))}
                            className="text-center text-lg font-bold w-16 h-10 border border-gray-300 focus:border-blue-500 focus:ring-blue-500 rounded-lg"
                          />
                          <span className="text-xs text-gray-500 mt-1">candidates</span>
                        </div>

                        {/* Increment Button */}
                        <button
                          onClick={() => handleSetThresholdNumberDetails(thresholdNumberDetails < filecount ? thresholdNumberDetails + 1 : filecount)}
                          className="p-2 bg-blue-600 hover:bg-blue-700 text-white rounded-lg transition-all duration-200"
                        >
                          <PlusCircleIcon className="w-4 h-4" />
                        </button>
                      </div>

                      <div className="text-center mt-2">
                        <span className="text-xs font-medium text-gray-700">
                          {thresholdNumberDetails} of {filecount} ({Math.round((thresholdNumberDetails / filecount) * 100)}%)
                        </span>
                      </div>
                    </div>
                  </div>
                </div>

                <div className="space-y-3">
                  <div className="bg-gray-50 rounded-lg p-3 border border-gray-200">
                    <h4 className="text-sm font-semibold text-gray-900 mb-2">Assessment Summary</h4>
                    <div className="space-y-2">
                      <div className="flex justify-between items-center text-xs">
                        <span className="text-gray-600">Total CVs</span>
                        <span className="font-bold text-blue-600">{filecount}</span>
                      </div>
                      <div className="flex justify-between items-center text-xs">
                        <span className="text-gray-600">Shortlist Size</span>
                        <span className="font-bold text-green-600">{thresholdNumberDetails}</span>
                      </div>
                      <div className="flex justify-between items-center text-xs">
                        <span className="text-gray-600">Selection Rate</span>
                        <span className="font-bold text-purple-600">{Math.round((thresholdNumberDetails / filecount) * 100)}%</span>
                      </div>
                    </div>
                  </div>

                  {/* Report types explanation */}
                  <div className="bg-purple-50 rounded-lg p-3 border border-purple-100">
                    <h4 className="text-sm font-semibold text-purple-900 mb-2">Report Types Available:</h4>
                    <div className="space-y-2 text-xs text-purple-800">
                      <div className="flex items-start gap-2">
                        <div className="w-1.5 h-1.5 bg-purple-500 rounded-full mt-1.5"></div>
                        <div>
                          <strong>Shortlist Report:</strong> Top {thresholdNumberDetails} candidates with detailed scores
                        </div>
                      </div>
                      <div className="flex items-start gap-2">
                        <div className="w-1.5 h-1.5 bg-purple-500 rounded-full mt-1.5"></div>
                        <div>
                          <strong>Complete Report:</strong> All {filecount} candidates with comprehensive analysis
                        </div>
                      </div>
                      <div className="flex items-start gap-2">
                        <div className="w-1.5 h-1.5 bg-purple-500 rounded-full mt-1.5"></div>
                        <div>
                          <strong>Assessment Package:</strong> Everything including raw data and insights
                        </div>
                      </div>
                    </div>
                  </div>

                  {/* Processing time estimate */}
                  <div className="bg-green-50 rounded-lg p-3 border border-green-100">
                    <h4 className="text-sm font-semibold text-green-900 mb-1">Processing Time:</h4>
                    <p className="text-xs text-green-800">
                      Estimated {Math.ceil(filecount / 10)} minute(s) for {filecount} CVs
                    </p>
                  </div>
                </div>
              </div>
            </div>

        )}

        {/* Generate Button */}
        <div className="flex justify-center mt-4">
          <button
            onClick={() => {
              if (isReportGenerated) {
                window.location.href = `/home/<USER>/projectlist/${projectId}`;
              } else {
                handleGenerateReport();
              }
            }}
            disabled={isLoading}
            className={`py-3 px-6 text-sm font-semibold rounded-lg shadow-md hover:shadow-lg transition-all duration-300 flex items-center justify-center gap-2 ${
              isLoading
                ? "bg-gray-400 cursor-not-allowed"
                : isReportGenerated
                ? "bg-green-600 hover:bg-green-700"
                : "bg-blue-600 hover:bg-blue-700"
            } text-white`}
          >
            {/* Loading Spinner */}
            {isLoading && (
              <svg
                aria-hidden="true"
                className="w-4 h-4 animate-spin"
                viewBox="0 0 100 101"
                fill="none"
                xmlns="http://www.w3.org/2000/svg"
              >
                <path
                  d="M100 50.5908C100 78.2051 77.6142 100.591 50 100.591C22.3858 100.591 0 78.2051 0 50.5908C0 22.9766 22.3858 0.59082 50 0.59082C77.6142 0.59082 100 22.9766 100 50.5908ZM9.08144 50.5908C9.08144 73.1895 27.4013 91.5094 50 91.5094C72.5987 91.5094 90.9186 73.1895 90.9186 50.5908C90.9186 27.9921 72.5987 9.67226 50 9.67226C27.4013 9.67226 9.08144 27.9921 9.08144 50.5908Z"
                  fill="currentColor"
                />
                <path
                  d="M93.9676 39.0409C96.393 38.4038 97.8624 35.9116 97.0079 33.5539C95.2932 28.8227 92.871 24.3692 89.8167 20.348C85.8452 15.1192 80.8826 10.7238 75.2124 7.41289C69.5422 4.10194 63.2754 1.94025 56.7698 1.05124C51.7666 0.367541 46.6976 0.446843 41.7345 1.27873C39.2613 1.69328 37.813 4.19778 38.4501 6.62326C39.0873 9.04874 41.5694 10.4717 44.0505 10.1071C47.8511 9.54855 51.7191 9.52689 55.5402 10.0491C60.8642 10.7766 65.9928 12.5457 70.6331 15.2552C75.2735 17.9648 79.3347 21.5619 82.5849 25.841C84.9175 28.9121 86.7997 32.2913 88.1811 35.8758C89.083 38.2158 91.5421 39.6781 93.9676 39.0409Z"
                  fill="currentFill"
                />
              </svg>
            )}
            {!isLoading && !isReportGenerated && <SparklesIcon className="w-4 h-4" />}
            {isLoading
              ? "Generating Report..."
              : isReportGenerated
              ? "View Results"
              : "Generate Report"
            }
          </button>
        </div>

        {/* Report Actions */}
        {isReportGenerated && (
          <div className="mt-8 bg-white rounded-xl shadow-sm border border-gray-200 p-8 relative overflow-hidden">
            {/* Background decoration */}
            <div className="absolute top-0 right-0 w-32 h-32 bg-gradient-to-br from-green-50 to-transparent rounded-full -translate-y-16 translate-x-16"></div>

            <div className="relative">
              <div className="text-center mb-8">
                <div className="inline-flex items-center justify-center w-16 h-16 bg-gradient-to-br from-green-500 to-green-600 rounded-full mb-4">
                  <CheckBadgeIcon className="w-8 h-8 text-white" />
                </div>
                <h3 className="text-2xl font-bold text-gray-900 mb-3">
                  Assessment Report Generated Successfully! 🎉
                </h3>
                <p className="text-lg text-gray-600">
                  Your comprehensive talent assessment report is ready. Download or view the detailed results below.
                </p>
              </div>

              <div className="space-y-6">
                {fileNameCV ? (
                  <div className="bg-blue-50 rounded-xl p-6 border border-blue-100">
                    <h4 className="text-lg font-semibold text-blue-900 mb-4">Individual Assessment Report</h4>
                    <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
                      <button
                        className="px-6 py-3 bg-gradient-to-r from-blue-600 to-blue-700 hover:from-blue-700 hover:to-blue-800 text-white font-medium rounded-lg transition-all duration-200 transform hover:scale-105 shadow-lg hover:shadow-xl"
                        onClick={() => { getDownloadReport(selectedRunId, 2); }}
                      >
                        📄 Download Report
                      </button>
                      <Link
                        href={`/home/<USER>
                        rel="noopener noreferrer"
                        target="_blank"
                        className="px-6 py-3 bg-white hover:bg-gray-50 text-blue-600 font-medium rounded-lg border-2 border-blue-600 hover:border-blue-700 transition-all duration-200 text-center transform hover:scale-105 shadow-lg hover:shadow-xl"
                      >
                        👁️ View Report Online
                      </Link>
                    </div>
                  </div>
                ) : (
                  <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                    <div className="bg-blue-50 rounded-xl p-6 border border-blue-100">
                      <div className="flex items-center gap-3 mb-4">
                        <div className="w-3 h-3 bg-blue-500 rounded-full"></div>
                        <h4 className="text-lg font-semibold text-blue-900">Shortlist Report</h4>
                      </div>
                      <p className="text-sm text-blue-700 mb-4">
                        Top {thresholdNumberDetails} candidates ranked by assessment score
                      </p>
                      <div className="space-y-3">
                        <button
                          className="w-full px-4 py-3 bg-gradient-to-r from-blue-600 to-blue-700 hover:from-blue-700 hover:to-blue-800 text-white font-medium rounded-lg transition-all duration-200 transform hover:scale-105 shadow-lg hover:shadow-xl"
                          onClick={() => { getDownloadReport(selectedRunId, 3); }}
                        >
                          📋 Download Shortlist
                        </button>
                        <Link
                          href={`/home/<USER>
                          rel="noopener noreferrer"
                          target="_blank"
                          className="block w-full px-4 py-3 bg-white hover:bg-gray-50 text-blue-600 font-medium rounded-lg border-2 border-blue-600 hover:border-blue-700 transition-all duration-200 text-center transform hover:scale-105 shadow-lg hover:shadow-xl"
                        >
                          👁️ View Shortlist Online
                        </Link>
                      </div>
                    </div>

                    <div className="bg-purple-50 rounded-xl p-6 border border-purple-100">
                      <div className="flex items-center gap-3 mb-4">
                        <div className="w-3 h-3 bg-purple-500 rounded-full"></div>
                        <h4 className="text-lg font-semibold text-purple-900">Complete Report</h4>
                      </div>
                      <p className="text-sm text-purple-700 mb-4">
                        Detailed analysis of all {filecount} candidates with scores and insights
                      </p>
                      <div className="space-y-3">
                        <button
                          className="w-full px-4 py-3 bg-gradient-to-r from-purple-600 to-purple-700 hover:from-purple-700 hover:to-purple-800 text-white font-medium rounded-lg transition-all duration-200 transform hover:scale-105 shadow-lg hover:shadow-xl"
                          onClick={() => { getDownloadReport(selectedRunId, 2); }}
                        >
                          📊 Download Full Report
                        </button>
                        <Link
                          href={`/home/<USER>
                          rel="noopener noreferrer"
                          target="_blank"
                          className="block w-full px-4 py-3 bg-white hover:bg-gray-50 text-purple-600 font-medium rounded-lg border-2 border-purple-600 hover:border-purple-700 transition-all duration-200 text-center transform hover:scale-105 shadow-lg hover:shadow-xl"
                        >
                          👁️ View Full Report Online
                        </Link>
                      </div>
                    </div>
                  </div>
                )}

                <div className="bg-gradient-to-r from-green-50 to-emerald-50 rounded-xl p-6 border border-green-200">
                  <div className="text-center">
                    <h4 className="text-lg font-semibold text-green-900 mb-3">🎁 Complete Assessment Package</h4>
                    <p className="text-sm text-green-700 mb-4">
                      Download everything: reports, candidate profiles, assessment data, and analysis summaries
                    </p>
                    <button
                      className="px-8 py-3 bg-gradient-to-r from-green-600 to-green-700 hover:from-green-700 hover:to-green-800 text-white font-semibold rounded-lg transition-all duration-200 transform hover:scale-105 shadow-lg hover:shadow-xl"
                      onClick={() => { getDownloadReport(selectedRunId, 4); }}
                    >
                      📦 Download Complete Package
                    </button>
                  </div>
                </div>
              </div>
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

export default InputThresholdValue;
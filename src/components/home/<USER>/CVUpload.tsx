"use client";
import React, { useEffect, useState } from "react";
import {
  CloudArrowUpIcon,
} from "@heroicons/react/20/solid";
import {
  CheckBadgeIcon,
  InformationCircleIcon,
  XCircleIcon,
  ArrowRightIcon
} from "@heroicons/react/24/outline";
import { usePostUploadCV } from "@/hooks/home/<USER>/usePostUploadCV";
import { useRemoveUploadedCV } from "@/hooks/home/<USER>/cv/useRemoveUploadedCV";
import JSZip from 'jszip'; // Import JSZip library

interface CVUploadProps {
  selectedRunId?: number;
  setFileNameCV?: (fileName: string) => void;
  setFileNameZip?: (fileName: string) => void;
  setSelectedTab?: (tab: number) => void;
  setFilecount?: (filecount: number) => void;
}

export default function CVUpload({ selectedRunId, setFilecount, setFileNameCV = () => {}, setFileNameZip = () => {}, setSelectedTab = () => {} }: CVUploadProps) {
  const [fileNameCVDetail, setFileNameCVDetail] = useState("");
  const [fileNameZipDetail, setFileNameZipDetail] = useState("");
  const [file, setFile] = useState<File | undefined>(undefined);
  const [modalType, setModalType] = useState(null);
  const [modalMessage, setModalMessage] = useState("");
  const [selectedSection, setSelectedSection] = useState<"single" | "compressed" | null>(null); // Track selected section

  const postcv = usePostUploadCV(selectedRunId, file);
  const deletecv = useRemoveUploadedCV(selectedRunId)

  const handleSetFileNameCVDetail = (stringValue: string) => {
    setFileNameCV(stringValue)
    setFileNameCVDetail(stringValue)
  }

  const handleSetFileNameZipDetail = (stringValue: string) => {
    setFileNameZip(stringValue)
    setFileNameZipDetail(stringValue)
  }

  const countFilesInZip = async (file: File) => {
    const zip = new JSZip();
    const content = await zip.loadAsync(file);
    const fileCount = Object.keys(content.files).length;
    return fileCount;
  };

  const handleUploadCVnZIP = async (e: React.ChangeEvent<HTMLInputElement> | React.DragEvent<HTMLDivElement>, type: "single" | "compressed") => {
    e.preventDefault();

    let uploadedFile: File | null = null;

    if ('dataTransfer' in e && e.dataTransfer?.files) {
      uploadedFile = e.dataTransfer.files[0];
    } else if (e.target.files) {
      uploadedFile = e.target.files[0];
    }
    setFile(uploadedFile || undefined); // Update the file state

    if (!uploadedFile) {
      setModalType("err");
      setModalMessage("No file found");
      setFile(undefined);
      return;
    }

    const validSingleFileTypes = [
      "application/pdf",
      "application/vnd.openxmlformats-officedocument.wordprocessingml.document"
    ];

    const ValidCompressedTypes = [
      "application/zip",
      "application/x-zip-compressed"
    ];

    if (type == "single") {
      if (!validSingleFileTypes.includes(uploadedFile.type)) {
        setModalType("err");
        setModalMessage("Please upload a valid file (PDF or DOCX)");
        return;
      }
    } else if (type == "compressed") {
      if (!ValidCompressedTypes.includes(uploadedFile.type)) {
        setModalType("err");
        setModalMessage("Please upload a valid file (.ZIP)");
        return;
      }
      // Count the number of files in the zip
      const fileCount = await countFilesInZip(uploadedFile);
      setFilecount(fileCount)
    } else {
      setModalType("err");
      setModalMessage("Not Valid File type");
      return;
    }

    try {
      const response = await postcv.mutateAsync(selectedRunId, uploadedFile);

      if (response?.msg) {
        setModalType("msg");
        setModalMessage(response.msg);
        if (type == "single") {
          handleSetFileNameCVDetail(`${uploadedFile.name}`);
          setSelectedSection("single"); // Set selected section
        } else if (type == "compressed") {
          handleSetFileNameZipDetail(`${uploadedFile.name}`);
          setSelectedSection("compressed"); // Set selected section
        } else {
          return;
        }
        setFile(undefined);
      } else if (response?.err) {
        setModalType("err");
        setModalMessage(response.err);
      } else if (response?.detail) {
        setModalType("err");
        setModalMessage(response.detail);
      }

      if (Array.isArray(response) && response.length > 0) {
        const message = response[0];
      } else {
        console.error("Response is empty or not an array");
      }
    } catch (error) {
      console.error("Error uploading JD file:", error);
      setModalType("err");
      setModalMessage("An error occurred while uploading the file. Please try again.");
    }
  };

  const handleRemoveCVnZIP = async () => {
    try {
      const response = await deletecv.mutateAsync(selectedRunId);

      if (response.msg) {
        setFile(null)
        handleSetFileNameCVDetail("");
        handleSetFileNameZipDetail("")
        setModalType("msg");
        setModalMessage(response?.msg);
        setSelectedSection(null); // Reset selected section
      }

      if (response.err) {
        setModalType("err");
        setModalMessage(response?.err);
      }

      if (response.detail) {
        setModalType("err");
        setModalMessage(response?.detail);
      }

      if (response.msg) {
        handleSetFileNameCVDetail("");
      }

    } catch (error) {
      console.error("Error deleting CV:", error);
      setModalType("err");
      setModalMessage(`Error deleting CV: ${error}`);
    }
  }

  return (
    <div className="flex justify-center items-center h-full w-full p-4">
      <div className="w-full max-w-4xl">
        {/* Header Section */}
        <div className="text-center mb-4">
          <div className="inline-flex items-center justify-center w-12 h-12 bg-gradient-to-br from-blue-500 to-blue-600 rounded-full mb-2">
            <CloudArrowUpIcon className="w-6 h-6 text-white" />
          </div>
          <h2 className="text-xl font-bold text-gray-900 mb-1">
            Upload Candidate CVs
          </h2>
          <p className="text-sm text-gray-600 max-w-lg mx-auto">
            Upload candidate resumes for assessment. Choose single CV or bulk ZIP upload.
          </p>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-6">
          {/* Single CV Upload */}
          <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-5 relative overflow-hidden">
            {/* Background decoration */}
            <div className="absolute top-0 right-0 w-20 h-20 bg-gradient-to-br from-blue-50 to-transparent rounded-full -translate-y-10 translate-x-10"></div>

            <div className="relative">
              <div className="flex items-center gap-2 mb-4">
                <div className="flex items-center gap-2">
                  <h3 className="text-lg font-semibold text-gray-900">Single CV Upload</h3>
                  <div className="relative group">
                    <InformationCircleIcon className="w-4 h-4 text-gray-500" />
                    <div className="absolute bottom-0 left-1/2 transform -translate-x-1/2 mb-6 hidden group-hover:flex justify-center items-center p-2 bg-gray-800 text-white text-xs rounded-lg shadow-lg whitespace-nowrap z-10">
                      Upload one CV file for individual assessment
                    </div>
                  </div>
                </div>
                {selectedSection === "single" && (
                  <div className="px-2 py-1 bg-blue-100 text-blue-800 text-xs font-medium rounded-full">
                    Selected
                  </div>
                )}
              </div>

              <div
                className={`border-2 border-dashed rounded-lg p-6 text-center transition-all duration-300 ${
                  selectedSection === "single"
                    ? "border-blue-400 bg-blue-50 shadow-md"
                    : fileNameCVDetail || fileNameZipDetail
                    ? "border-gray-200 bg-gray-50"
                    : "border-gray-300 hover:border-blue-400 hover:bg-blue-50"
                }`}
                onDrop={(e) => {
                  if (!fileNameCVDetail && !fileNameZipDetail) {
                    handleUploadCVnZIP(e, "single");
                  }
                }}
                onDragOver={(e) => e.preventDefault()}
              >
                {fileNameCVDetail ? (
                  <div className="flex flex-col items-center gap-3">
                    <div className="rounded-full bg-green-100 p-2">
                      <CheckBadgeIcon className="w-6 h-6 text-green-600" />
                    </div>
                    <div className="text-center">
                      <div className="text-sm font-semibold text-green-800 mb-1">
                        CV Uploaded Successfully!
                      </div>
                      <div className="text-xs text-green-700 bg-green-100 px-3 py-1 rounded-full inline-block mb-2">
                        {fileNameCVDetail}
                      </div>
                      <div className="flex justify-center">
                        <button
                          onClick={handleRemoveCVnZIP}
                          className="text-xs text-red-600 hover:text-red-800 underline transition-colors"
                        >
                          Remove and upload different file
                        </button>
                      </div>
                    </div>
                  </div>
                ) : (
                  <div className="flex flex-col items-center gap-4">
                    <div className="rounded-full bg-blue-50 p-3">
                      <CloudArrowUpIcon className="w-8 h-8 text-blue-600" />
                    </div>
                    <div className="text-center">
                      <p className="text-sm font-semibold text-gray-900 mb-1">
                        Drag & drop CV here
                      </p>
                      <p className="text-xs text-gray-600 mb-3">
                        or click to select from your computer
                      </p>
                      <input
                        key={fileNameCVDetail ? "uploaded" : "not-uploaded"}
                        type="file"
                        id="fileCV"
                        className="hidden"
                        onChange={(e) => {
                          handleUploadCVnZIP(e, "single");
                        }}
                        disabled={!!fileNameCVDetail || !!fileNameZipDetail}
                        accept=".pdf,.docx"
                      />
                      <label
                        htmlFor="fileCV"
                        className={`inline-flex items-center px-4 py-2 text-sm font-medium rounded-lg cursor-pointer transition-all duration-200 transform hover:scale-105 shadow-md hover:shadow-lg ${
                          fileNameCVDetail || fileNameZipDetail
                            ? "bg-gray-100 text-gray-400 cursor-not-allowed"
                            : "bg-gradient-to-r from-blue-600 to-blue-700 hover:from-blue-700 hover:to-blue-800 text-white"
                        }`}
                      >
                        Choose CV File
                      </label>
                    </div>
                    <div className="flex items-center gap-2 text-xs text-gray-500">
                      <span>Supports PDF, DOCX</span>
                      <span>•</span>
                      <span>Max size: 10MB</span>
                    </div>
                  </div>
                )}
              </div>

              <div className="mt-3 bg-blue-50 rounded-lg p-3 border border-blue-100">
                <div className="flex items-start gap-2">
                  <InformationCircleIcon className="w-4 h-4 text-blue-600 mt-0.5 flex-shrink-0" />
                  <div>
                    <h5 className="text-xs font-medium text-blue-900 mb-1">Best for:</h5>
                    <p className="text-xs text-blue-700">
                      Individual candidate assessment, detailed analysis, or when you have just one CV to evaluate.
                    </p>
                  </div>
                </div>
              </div>
            </div>
          </div>

          {/* ZIP Upload */}
          <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-6 relative overflow-hidden">
            {/* Background decoration */}
            <div className="absolute top-0 right-0 w-24 h-24 bg-gradient-to-br from-green-50 to-transparent rounded-full -translate-y-12 translate-x-12"></div>

            <div className="relative">
              <div className="flex items-center gap-3 mb-6">
                <div className="flex items-center gap-2">
                  <h3 className="text-xl font-semibold text-gray-900">Bulk Upload (ZIP)</h3>
                  <div className="relative group">
                    <InformationCircleIcon className="w-5 h-5 text-gray-500" />
                    <div className="absolute bottom-0 left-1/2 transform -translate-x-1/2 mb-6 hidden group-hover:flex justify-center items-center p-3 bg-gray-800 text-white text-sm rounded-lg shadow-lg whitespace-nowrap z-10">
                      Upload multiple CV files in a ZIP archive for bulk assessment
                    </div>
                  </div>
                </div>
                {selectedSection === "compressed" && (
                  <div className="px-2 py-1 bg-green-100 text-green-800 text-xs font-medium rounded-full">
                    Selected
                  </div>
                )}
              </div>

              <div
                className={`border-2 border-dashed rounded-xl p-8 text-center transition-all duration-300 ${
                  selectedSection === "compressed"
                    ? "border-green-400 bg-green-50 shadow-md"
                    : fileNameCVDetail || fileNameZipDetail
                    ? "border-gray-200 bg-gray-50"
                    : "border-gray-300 hover:border-green-400 hover:bg-green-50"
                }`}
                onDrop={(e) => {
                  if (!fileNameCVDetail && !fileNameZipDetail) {
                    handleUploadCVnZIP(e, "compressed");
                  }
                }}
                onDragOver={(e) => e.preventDefault()}
              >
                {fileNameZipDetail ? (
                  <div className="flex flex-col items-center gap-4">
                    <div className="rounded-full bg-green-100 p-3">
                      <CheckBadgeIcon className="w-8 h-8 text-green-600" />
                    </div>
                    <div className="text-center">
                      <div className="text-lg font-semibold text-green-800 mb-2">
                        ZIP File Uploaded Successfully!
                      </div>
                      <div className="text-sm text-green-700 bg-green-100 px-4 py-2 rounded-full inline-block mb-3">
                        {fileNameZipDetail}
                      </div>
                      <div className="flex justify-center">
                        <button
                          onClick={handleRemoveCVnZIP}
                          className="text-sm text-red-600 hover:text-red-800 underline transition-colors"
                        >
                          Remove and upload different file
                        </button>
                      </div>
                    </div>
                  </div>
                ) : (
                  <div className="flex flex-col items-center gap-6">
                    <div className="rounded-full bg-green-50 p-4">
                      <CloudArrowUpIcon className="w-10 h-10 text-green-600" />
                    </div>
                    <div className="text-center">
                      <p className="text-lg font-semibold text-gray-900 mb-2">
                        Drag & drop ZIP file here
                      </p>
                      <p className="text-sm text-gray-600 mb-4">
                        or click to select from your computer
                      </p>
                      <input
                        key={fileNameZipDetail ? "uploaded" : "not-uploaded"}
                        type="file"
                        id="fileZipCV"
                        className="hidden"
                        onChange={(e) => {
                          handleUploadCVnZIP(e, "compressed");
                        }}
                        disabled={!!fileNameCVDetail || !!fileNameZipDetail}
                        accept=".zip"
                      />
                      <label
                        htmlFor="fileZipCV"
                        className={`inline-flex items-center px-6 py-3 font-medium rounded-lg cursor-pointer transition-all duration-200 transform hover:scale-105 shadow-lg hover:shadow-xl ${
                          fileNameCVDetail || fileNameZipDetail
                            ? "bg-gray-100 text-gray-400 cursor-not-allowed"
                            : "bg-gradient-to-r from-green-600 to-green-700 hover:from-green-700 hover:to-green-800 text-white"
                        }`}
                      >
                        Choose ZIP File
                      </label>
                    </div>
                    <div className="flex items-center gap-4 text-xs text-gray-500">
                      <span>Supports ZIP files</span>
                      <span>•</span>
                      <span>Max size: 50MB</span>
                    </div>
                  </div>
                )}
              </div>

              <div className="mt-4 bg-green-50 rounded-lg p-4 border border-green-100">
                <div className="flex items-start gap-3">
                  <InformationCircleIcon className="w-5 h-5 text-green-600 mt-0.5 flex-shrink-0" />
                  <div>
                    <h5 className="text-sm font-medium text-green-900 mb-1">Best for:</h5>
                    <p className="text-sm text-green-700">
                      Bulk candidate assessment, recruitment drives, or when you have multiple CVs to evaluate simultaneously.
                    </p>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Action Button */}
        <div className="flex justify-center">
          <button
            onClick={() => setSelectedTab(3)}
            disabled={!fileNameCVDetail && !fileNameZipDetail}
            className={`py-4 px-8 text-lg font-semibold rounded-xl shadow-lg hover:shadow-xl transition-all duration-300 flex items-center justify-center gap-3 ${
              fileNameCVDetail || fileNameZipDetail
                ? "bg-gradient-to-r from-blue-600 to-blue-700 hover:from-blue-700 hover:to-blue-800 text-white transform hover:scale-[1.02]"
                : "bg-gray-100 text-gray-400 cursor-not-allowed"
            }`}
          >
            Continue to Configuration <ArrowRightIcon className="w-5 h-5" />
          </button>
        </div>

        {/* Help Section */}
        <div className="mt-8 bg-gray-50 rounded-xl p-6 border border-gray-200">
          <div className="text-center mb-4">
            <h4 className="text-lg font-semibold text-gray-900">Need Help?</h4>
          </div>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6 text-sm text-gray-700">
            <div>
              <h5 className="font-medium text-gray-900 mb-2">Single CV Upload</h5>
              <ul className="space-y-1">
                <li>• Perfect for individual assessments</li>
                <li>• Detailed candidate analysis</li>
                <li>• Supports PDF and DOCX formats</li>
              </ul>
            </div>
            <div>
              <h5 className="font-medium text-gray-900 mb-2">ZIP Bulk Upload</h5>
              <ul className="space-y-1">
                <li>• Ideal for recruitment drives</li>
                <li>• Process multiple candidates at once</li>
                <li>• ZIP file containing PDF/DOCX files</li>
              </ul>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
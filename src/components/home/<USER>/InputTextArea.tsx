"use client";
import React, { useEffect, useState } from "react";

import {
  InformationCircleIcon,
  PencilSquareIcon,
} from "@heroicons/react/24/outline";
import { Textarea } from "@/components/ui/Textarea";
import { Label } from "@/components/ui/label";

interface InputTextAreaProps {
  setInfo: (value: string) => void;  // setter function to update the info state
  info: string;  // value to be displayed in the textarea
}

export default function InputTextArea({ setInfo, info }: InputTextAreaProps) {

  const [infoDetails, setinfoDetails] = useState(info)

  const handleInfoChanges = (e) => {
    setInfo(e.target.value)
    setinfoDetails(e.target.value)
  }

  return (
    <div className="w-full">
      <div className="bg-white rounded-lg border border-gray-200 p-4">
        <div className="flex items-center gap-2 mb-3">
          <PencilSquareIcon className="w-4 h-4 text-green-600" />
          <h3 className="text-sm font-semibold text-gray-900">
            Additional Context
          </h3>
        </div>

        <div className="space-y-3">
          <div className="bg-green-50 rounded-lg p-2 border border-green-100">
            <p className="text-xs text-green-800">
              <strong>Optional:</strong> Add specific requirements or preferences.
            </p>
          </div>

          <div className="space-y-2">
            <Label htmlFor="info" className="text-xs font-medium text-gray-700">
              Assessment Instructions
            </Label>
            <Textarea
              className="w-full min-h-[60px] resize-none border-gray-300 focus:border-green-500 focus:ring-green-500 rounded-lg text-xs"
              name="info"
              value={infoDetails}
              onChange={(e) => handleInfoChanges(e)}
              id="info"
              placeholder="Example: Focus on technical skills, prioritize startup experience..."
            />
            <div className="text-xs text-gray-500">
              {infoDetails.length} characters
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
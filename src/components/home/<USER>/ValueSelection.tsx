"use client";
import React, { useEffect, useState } from "react";
import {
  InformationCircleIcon,
  ArrowRightIcon
} from "@heroicons/react/24/outline";

interface ValueSelectionProps {
  handleRangeChange: (event: React.ChangeEvent<HTMLInputElement>) => void;
  setUseDefaultWeights: (value: boolean) => void;
  useDefaultWeights: boolean;
  setRangeValues: any;
  rangeValues: {
    range1: number;
    range2: number;
  };
  setSelectedTab: any;
}

export default function ValueSelection({
  setUseDefaultWeights,
  setRangeValues,
  useDefaultWeights,
  rangeValues,
  setSelectedTab
}: ValueSelectionProps) {
  const [useDefaultWeightsDetails, setUseDefaultWeightsDetails] = useState(useDefaultWeights);
  const [rangeValuesDetails, setRangeValuesDetails] = useState(rangeValues);

  const HandleSetUseDefaultWeightsDetails = (valueBoolean) => {
    setUseDefaultWeightsDetails(valueBoolean)
    setUseDefaultWeights(valueBoolean)
  }

  const handleRangeChange = (e) => {
    const { name, value } = e.target;
    setRangeValues((prevValues) => ({
      ...prevValues,
      [name]: value,
    }))
    setRangeValuesDetails((prevValues) => ({
      ...prevValues,
      [name]: value,
    }));
  };

  return (
    <div className="w-full">
      <div className="bg-white rounded-lg border border-gray-200 p-4">
        <div className="flex items-center gap-2 mb-3">
          <InformationCircleIcon className="w-4 h-4 text-purple-600" />
          <h3 className="text-sm font-semibold text-gray-900">
            Assessment Configuration
          </h3>
        </div>

        <div className="space-y-3">
          <div className="bg-purple-50 rounded-lg p-2 border border-purple-100">
            <p className="text-xs text-purple-800">
              <strong>Weights:</strong> Choose default or customize assessment weights.
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
            <button
              onClick={() => HandleSetUseDefaultWeightsDetails(true)}
              className={`p-3 rounded-lg border transition-all duration-300 text-left ${
                useDefaultWeightsDetails
                  ? "border-blue-500 bg-blue-50 text-blue-700"
                  : "border-gray-300 bg-white text-gray-700 hover:border-blue-300 hover:bg-blue-50"
              }`}
            >
              <div className="flex items-center gap-2 mb-1">
                <div className={`w-2 h-2 rounded-full ${
                  useDefaultWeightsDetails ? "bg-blue-500" : "bg-gray-300"
                }`}></div>
                <span className="text-xs font-semibold">Default Weights</span>
              </div>
              <p className="text-xs opacity-80">
                Recommended (100% each)
              </p>
            </button>

            <button
              onClick={() => HandleSetUseDefaultWeightsDetails(false)}
              className={`p-3 rounded-lg border transition-all duration-300 text-left ${
                !useDefaultWeightsDetails
                  ? "border-purple-500 bg-purple-50 text-purple-700"
                  : "border-gray-300 bg-white text-gray-700 hover:border-purple-300 hover:bg-purple-50"
              }`}
            >
              <div className="flex items-center gap-2 mb-1">
                <div className={`w-2 h-2 rounded-full ${
                  !useDefaultWeightsDetails ? "bg-purple-500" : "bg-gray-300"
                }`}></div>
                <span className="text-xs font-semibold">Custom Weights</span>
              </div>
              <p className="text-xs opacity-80">
                Fine-tune weights
              </p>
            </button>
              </div>
            </div>
          </div>
        

        {/* Custom Weights Configuration */}
        {!useDefaultWeightsDetails && (
          <div className="bg-gray-50 rounded-lg p-3 border border-gray-200 mt-3">
            <h4 className="text-xs font-semibold text-gray-900 mb-2">Custom Weights</h4>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
              <div className="space-y-2">
                <label className="text-xs font-medium text-blue-900">
                  JD-CV Similarity: {rangeValuesDetails.range1}%
                </label>
                <input
                  type="range"
                  name="range1"
                  min={0}
                  max={100}
                  value={rangeValuesDetails.range1}
                  onChange={handleRangeChange}
                  className="w-full h-2 bg-blue-200 rounded-lg appearance-none cursor-pointer"
                />
              </div>
              <div className="space-y-2">
                <label className="text-xs font-medium text-purple-900">
                  CV Consistency: {rangeValuesDetails.range2}%
                </label>
                <input
                  type="range"
                  name="range2"
                  min={0}
                  max={100}
                  value={rangeValuesDetails.range2}
                  onChange={handleRangeChange}
                  className="w-full h-2 bg-purple-200 rounded-lg appearance-none cursor-pointer"
                />
              </div>
            </div>
          </div>
        )}
      </div>
  );
};
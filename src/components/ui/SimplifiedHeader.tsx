"use client";
import { Fragment, useEffect, useRef, useState } from "react";
import { useRouter } from "next/navigation";
import Image from "next/image";
import { removeUser } from "@/api/user.localStorage";
import Link from 'next/link';
import { Disclosure, Menu, Transition } from "@headlessui/react";
import { useGetUserLogo } from '@/hooks/user/useGetUserLogo'
import { useGetUserDetailsWithLogo } from '@/hooks/user/useGetUserDetailsWithLogo';
import {
  Bars3Icon,
  ChevronDownIcon,
  XMarkIcon,
  InformationCircleIcon
} from "@heroicons/react/24/outline";
import {
  Cog6ToothIcon,
  ArrowRightOnRectangleIcon,
} from "@heroicons/react/20/solid";
import { useLogoutAll } from "@/hooks/useLogoutAll";
import Timer from "./timer/Timer";
import { getUser } from "@/api/user.localStorage"

const navigation = [
  { name: "Home", href: "/home" },
];

function classNames(...classes: any) {
  return classes.filter(Boolean).join(" ");
}

export default function SimplifiedHeader() {
  const [isOpenProject, setisOpenProject] = useState(false);
  const [isOpenClient, setisOpenClient] = useState(false);
  const router = useRouter();
  const [enabled, setEnabled] = useState(false);
  const user = getUser();

  const { data: userDataInfo, isLoading, isError } = useGetUserDetailsWithLogo(user?.user_id);
  const { data: userLogoData } = useGetUserLogo(userDataInfo?.user_logo);

  const getActiveItem = () => {
    const path = window.location.pathname || '';
    if (path.includes('/home/<USER>')) return 'project';
    if (path.includes('/home/<USER>')) return 'Client';
    if (path === '/home') return 'Home';
    return '';
  };

  const [activeItem, setActiveItem] = useState(getActiveItem());

  const toggleDropdownProject = () => { setisOpenProject(!isOpenProject); setisOpenClient(false); setActiveItem("project"); }
  const toggleDropdownClient = () => { setisOpenClient(!isOpenClient); setisOpenProject(false); setActiveItem("Client"); }

  useEffect(() => {
    setActiveItem(getActiveItem());
  }, []);

  const { data: logout } = useLogoutAll(enabled);
  const projectDropdownRef = useRef(null);

  const handleLogout = () => {
    removeUser(); // Call removeUser to clear user data
    window.history.replaceState(null, null, "/");
    router.replace("/login"); // Redirect to login page
    setEnabled(true);
  };

  const handleItemClick = (itemName, href, e) => {
    e.preventDefault();
    setActiveItem(itemName);
    router.push(href);
  };

  return (
    <Disclosure as="nav" className="bg-white shadow-sm border-b border-gray-200">
      {({ open }) => (
        <>
          <div className="mx-auto max-w-7xl px-4 sm:px-6 lg:px-8">
            <div className="relative flex h-16 items-center justify-between">
              <div className="absolute inset-y-0 left-0 flex items-center sm:hidden">
                {/* Mobile menu button*/}
                <Disclosure.Button className="relative inline-flex items-center justify-center rounded-md p-2 text-gray-400 hover:bg-gray-100 hover:text-gray-500 focus:outline-none focus:ring-2 focus:ring-inset focus:ring-blue-500">
                  <span className="absolute -inset-0.5" />
                  <span className="sr-only">Open main menu</span>
                  {open ? (
                    <XMarkIcon className="block h-6 w-6" aria-hidden="true" />
                  ) : (
                    <Bars3Icon className="block h-6 w-6" aria-hidden="true" />
                  )}
                </Disclosure.Button>
              </div>
              
              <div className="flex flex-1 items-center justify-center sm:items-stretch sm:justify-start">
                <div className="flex flex-shrink-0 items-center">
                  <Link href="/home">
                    <span className="text-xl font-bold text-gray-900">HR-Math</span>
                  </Link>
                </div>
                
                <div className="hidden sm:ml-6 sm:block">
                  <div className="flex space-x-4">
                    {navigation.map((item) => (
                      <Link
                        key={item.name}
                        href={item.href}
                        onClick={(e) => handleItemClick(item.name, item.href, e)}
                        className={classNames(
                          item.name === activeItem
                            ? 'bg-blue-50 text-blue-700'
                            : 'text-gray-700 hover:bg-gray-100',
                          'group flex items-center px-4 py-2 text-sm font-medium rounded-md transition-colors duration-200'
                        )}
                        aria-current={item.name === activeItem ? 'page' : undefined}
                      >
                        {item.name}
                      </Link>
                    ))}
                    
                    {/* Projects Dropdown */}
                    <div className="relative">
                      <button
                        className={classNames(
                          "project" === activeItem
                            ? 'bg-blue-50 text-blue-700'
                            : 'text-gray-700 hover:bg-gray-100',
                          'group flex items-center px-4 py-2 text-sm font-medium rounded-md transition-colors duration-200'
                        )}
                        onClick={toggleDropdownProject}
                      >
                        Projects
                        <ChevronDownIcon className={classNames(
                          'ml-2 h-5 w-5 transition-transform duration-200',
                          isOpenProject ? 'rotate-180' : ''
                        )} />
                      </button>
                      
                      {isOpenProject && (
                        <div className="absolute z-50 mt-1 w-48 origin-top-right rounded-md bg-white shadow-lg ring-1 ring-gray-200 divide-y divide-gray-100">
                          <Link
                            href="/home/<USER>/create/null?clientId=null"
                            className="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-50 transition-colors duration-200"
                          >
                            Create Project
                          </Link>
                          <Link
                            href="/home/<USER>/projectlist"
                            className="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-50 transition-colors duration-200"
                          >
                            Project List
                          </Link>
                        </div>
                      )}
                    </div>
                    
                    {/* Clients Dropdown */}
                    <div className="relative">
                      <button
                        className={classNames(
                          "Client" === activeItem
                            ? 'bg-blue-50 text-blue-700'
                            : 'text-gray-700 hover:bg-gray-100',
                          'group flex items-center px-4 py-2 text-sm font-medium rounded-md transition-colors duration-200'
                        )}
                        onClick={toggleDropdownClient}
                      >
                        Clients
                        <ChevronDownIcon className={classNames(
                          'ml-2 h-5 w-5 transition-transform duration-200',
                          isOpenClient ? 'rotate-180' : ''
                        )} />
                      </button>
                      
                      {isOpenClient && (
                        <div className="absolute z-50 mt-1 w-48 origin-top-right rounded-md bg-white shadow-lg ring-1 ring-gray-200 divide-y divide-gray-100">
                          <Link
                            href="/home"
                            className="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-50 transition-colors duration-200"
                          >
                            Create Client
                          </Link>
                          <Link
                            href="/home/<USER>"
                            className="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-50 transition-colors duration-200"
                          >
                            Client List
                          </Link>
                        </div>
                      )}
                    </div>
                  </div>
                </div>
              </div>
              
              <div className="absolute inset-y-0 right-0 flex items-center pr-2 sm:static sm:inset-auto sm:ml-6 sm:pr-0">
                <div className="hidden sm:block mr-4">
                  <Timer handleLogout={handleLogout} timeDetail={1800} />
                </div>
                
                {/* Profile dropdown */}
                <Menu as="div" className="relative ml-3">
                  <div>
                    <Menu.Button className="relative flex rounded-full bg-white text-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2">
                      <span className="absolute -inset-1.5" />
                      <span className="sr-only">Open user menu</span>
                      <Image
                        className="h-8 w-8 rounded-full"
                        src={(userDataInfo?.user_logo == null ? '/user.png' : userLogoData)}
                        alt="User"
                        width={32}
                        height={32}
                      />
                    </Menu.Button>
                  </div>
                  <Transition
                    as={Fragment}
                    enter="transition ease-out duration-100"
                    enterFrom="transform opacity-0 scale-95"
                    enterTo="transform opacity-100 scale-100"
                    leave="transition ease-in duration-75"
                    leaveFrom="transform opacity-100 scale-100"
                    leaveTo="transform opacity-0 scale-95"
                  >
                    <Menu.Items className="absolute right-0 z-10 mt-2 w-48 origin-top-right rounded-md bg-white py-1 shadow-lg ring-1 ring-black ring-opacity-5 focus:outline-none">
                      <Menu.Item>
                        {({ active }) => (
                          <Link
                            href="/home/<USER>"
                            className={classNames(
                              active ? 'bg-gray-100' : '',
                              'flex items-center px-4 py-2 text-sm text-gray-700'
                            )}
                          >
                            <Cog6ToothIcon className="mr-3 h-5 w-5 text-gray-400" aria-hidden="true" />
                            Profile
                          </Link>
                        )}
                      </Menu.Item>
                      <Menu.Item>
                        {({ active }) => (
                          <button
                            onClick={handleLogout}
                            className={classNames(
                              active ? 'bg-gray-100' : '',
                              'flex w-full items-center px-4 py-2 text-sm text-gray-700'
                            )}
                          >
                            <ArrowRightOnRectangleIcon className="mr-3 h-5 w-5 text-gray-400" aria-hidden="true" />
                            Sign out
                          </button>
                        )}
                      </Menu.Item>
                    </Menu.Items>
                  </Transition>
                </Menu>
              </div>
            </div>
          </div>

          <Disclosure.Panel className="sm:hidden">
            <div className="space-y-1 px-2 pb-3 pt-2">
              {navigation.map((item) => (
                <Disclosure.Button
                  key={item.name}
                  as="a"
                  href={item.href}
                  className={classNames(
                    item.name === activeItem
                      ? 'bg-blue-50 text-blue-700'
                      : 'text-gray-700 hover:bg-gray-100',
                    'block rounded-md px-3 py-2 text-base font-medium'
                  )}
                  aria-current={item.name === activeItem ? 'page' : undefined}
                >
                  {item.name}
                </Disclosure.Button>
              ))}
              
              <Disclosure>
                {({ open }) => (
                  <>
                    <Disclosure.Button
                      className={classNames(
                        "project" === activeItem
                          ? 'bg-blue-50 text-blue-700'
                          : 'text-gray-700 hover:bg-gray-100',
                        'flex w-full items-center justify-between rounded-md px-3 py-2 text-base font-medium'
                      )}
                    >
                      <span>Projects</span>
                      <ChevronDownIcon
                        className={classNames(
                          open ? 'rotate-180' : '',
                          'h-5 w-5 transition-transform'
                        )}
                      />
                    </Disclosure.Button>
                    <Disclosure.Panel className="px-4 pt-2 pb-2 text-sm text-gray-500">
                      <Link
                        href="/home/<USER>/create/null?clientId=null"
                        className="block py-2 text-gray-700 hover:text-blue-700"
                      >
                        Create Project
                      </Link>
                      <Link
                        href="/home/<USER>/projectlist"
                        className="block py-2 text-gray-700 hover:text-blue-700"
                      >
                        Project List
                      </Link>
                    </Disclosure.Panel>
                  </>
                )}
              </Disclosure>
              
              <Disclosure>
                {({ open }) => (
                  <>
                    <Disclosure.Button
                      className={classNames(
                        "Client" === activeItem
                          ? 'bg-blue-50 text-blue-700'
                          : 'text-gray-700 hover:bg-gray-100',
                        'flex w-full items-center justify-between rounded-md px-3 py-2 text-base font-medium'
                      )}
                    >
                      <span>Clients</span>
                      <ChevronDownIcon
                        className={classNames(
                          open ? 'rotate-180' : '',
                          'h-5 w-5 transition-transform'
                        )}
                      />
                    </Disclosure.Button>
                    <Disclosure.Panel className="px-4 pt-2 pb-2 text-sm text-gray-500">
                      <Link
                        href="/home"
                        className="block py-2 text-gray-700 hover:text-blue-700"
                      >
                        Create Client
                      </Link>
                      <Link
                        href="/home/<USER>"
                        className="block py-2 text-gray-700 hover:text-blue-700"
                      >
                        Client List
                      </Link>
                    </Disclosure.Panel>
                  </>
                )}
              </Disclosure>
              
              <div className="px-3 py-2">
                <Timer handleLogout={handleLogout} timeDetail={1800} />
              </div>
            </div>
          </Disclosure.Panel>
        </>
      )}
    </Disclosure>
  );
}
